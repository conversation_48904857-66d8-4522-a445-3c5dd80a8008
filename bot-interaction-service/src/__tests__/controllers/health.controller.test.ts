/**
 * Health Controller Tests
 */

import { Request, Response } from 'express';
import { HealthController } from '../../controllers/health.controller';
import { RedisService } from '../../services/redis.service';
import { NLUService } from '../../services/nlu.service';
import { DatabaseConnection } from '@neuratalk/bot-store';

// Mock dependencies
jest.mock('../../services/redis.service');
jest.mock('../../services/nlu.service');
jest.mock('@neuratalk/bot-store');

describe('HealthController', () => {
  let healthController: HealthController;
  let mockRedisService: jest.Mocked<RedisService>;
  let mockDatabaseConnection: jest.Mocked<DatabaseConnection>;
  let mockNLUService: jest.Mocked<NLUService>;
  let mockReq: Partial<Request>;
  let mockRes: Partial<Response>;

  beforeEach(() => {
    mockRedisService = {
      healthCheck: jest.fn()
    } as any;

    mockDatabaseConnection = {
      healthCheck: jest.fn()
    } as any;

    mockNLUService = {
      healthCheck: jest.fn()
    } as any;

    mockReq = {};
    mockRes = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn()
    };

    healthController = new HealthController(
      mockRedisService,
      mockDatabaseConnection,
      mockNLUService
    );
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('healthCheck', () => {
    it('should return healthy status when all dependencies are healthy', async () => {
      mockRedisService.healthCheck.mockResolvedValue({
        status: 'healthy',
        responseTime: 10
      });
      mockDatabaseConnection.healthCheck.mockResolvedValue({
        status: 'healthy',
        responseTime: 15
      });
      mockNLUService.healthCheck.mockResolvedValue({
        status: 'healthy',
        responseTime: 20
      });

      await healthController.healthCheck(mockReq as Request, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        success: true,
        data: expect.objectContaining({
          status: 'healthy',
          timestamp: expect.any(Date),
          version: expect.any(String),
          uptime: expect.any(Number),
          dependencies: expect.arrayContaining([
            expect.objectContaining({
              name: 'redis',
              status: 'healthy',
              responseTime: 10
            }),
            expect.objectContaining({
              name: 'database',
              status: 'healthy',
              responseTime: 15
            }),
            expect.objectContaining({
              name: 'nlu_service',
              status: 'healthy',
              responseTime: 20
            })
          ])
        }),
        timestamp: expect.any(Date)
      });
    });

    it('should return degraded status when some dependencies are unhealthy', async () => {
      mockRedisService.healthCheck.mockResolvedValue({
        status: 'healthy',
        responseTime: 10
      });
      mockDatabaseConnection.healthCheck.mockResolvedValue({
        status: 'healthy',
        responseTime: 15
      });
      mockNLUService.healthCheck.mockResolvedValue({
        status: 'unhealthy',
        error: 'Connection failed'
      });

      await healthController.healthCheck(mockReq as Request, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        success: true,
        data: expect.objectContaining({
          status: 'degraded'
        }),
        timestamp: expect.any(Date)
      });
    });

    it('should return unhealthy status when all dependencies are unhealthy', async () => {
      mockRedisService.healthCheck.mockResolvedValue({
        status: 'unhealthy'
      });
      mockDatabaseConnection.healthCheck.mockResolvedValue({
        status: 'unhealthy'
      });
      mockNLUService.healthCheck.mockResolvedValue({
        status: 'unhealthy'
      });

      await healthController.healthCheck(mockReq as Request, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(503);
      expect(mockRes.json).toHaveBeenCalledWith({
        success: true,
        data: expect.objectContaining({
          status: 'unhealthy'
        }),
        timestamp: expect.any(Date)
      });
    });

    it('should handle errors gracefully', async () => {
      mockRedisService.healthCheck.mockRejectedValue(new Error('Redis error'));

      await healthController.healthCheck(mockReq as Request, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(503);
      expect(mockRes.json).toHaveBeenCalledWith({
        success: false,
        error: {
          code: 'HEALTH_CHECK_ERROR',
          message: 'Health check failed',
          error: expect.any(Error)
        },
        timestamp: expect.any(Date)
      });
    });
  });

  describe('readinessCheck', () => {
    it('should return ready status when critical dependencies are healthy', async () => {
      mockRedisService.healthCheck.mockResolvedValue({
        status: 'healthy',
        responseTime: 10
      });
      mockDatabaseConnection.healthCheck.mockResolvedValue({
        status: 'healthy',
        responseTime: 15
      });

      await healthController.readinessCheck(mockReq as Request, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        success: true,
        data: { status: 'ready' },
        timestamp: expect.any(Date)
      });
    });

    it('should return not ready status when critical dependencies are unhealthy', async () => {
      mockRedisService.healthCheck.mockResolvedValue({
        status: 'unhealthy'
      });
      mockDatabaseConnection.healthCheck.mockResolvedValue({
        status: 'healthy',
        responseTime: 15
      });

      await healthController.readinessCheck(mockReq as Request, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(503);
      expect(mockRes.json).toHaveBeenCalledWith({
        success: false,
        error: {
          code: 'NOT_READY',
          message: 'Service is not ready'
        },
        timestamp: expect.any(Date)
      });
    });

    it('should handle errors gracefully', async () => {
      mockRedisService.healthCheck.mockRejectedValue(new Error('Redis error'));

      await healthController.readinessCheck(mockReq as Request, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(503);
      expect(mockRes.json).toHaveBeenCalledWith({
        success: false,
        error: {
          code: 'READINESS_CHECK_ERROR',
          message: 'Readiness check failed'
        },
        timestamp: expect.any(Date)
      });
    });
  });

  describe('livenessCheck', () => {
    it('should always return alive status', async () => {
      await healthController.livenessCheck(mockReq as Request, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        success: true,
        data: {
          status: 'alive',
          uptime: expect.any(Number)
        },
        timestamp: expect.any(Date)
      });
    });
  });

  describe('metrics', () => {
    it('should return service metrics', () => {
      healthController.metrics(mockReq as Request, mockRes as Response);

      expect(mockRes.json).toHaveBeenCalledWith({
        success: true,
        data: expect.objectContaining({
          uptime: expect.any(Number),
          memory: expect.objectContaining({
            rss: expect.any(Number),
            heapTotal: expect.any(Number),
            heapUsed: expect.any(Number),
            external: expect.any(Number)
          }),
          cpu: expect.any(Object),
          environment: expect.any(String),
          nodeVersion: expect.any(String),
          pid: expect.any(Number)
        }),
        timestamp: expect.any(Date)
      });
    });

    it('should handle errors gracefully', () => {
      // Mock process.memoryUsage to throw an error
      const originalMemoryUsage = process.memoryUsage;
      process.memoryUsage = jest.fn().mockImplementation(() => {
        throw new Error('Memory usage error');
      }) as unknown as NodeJS.MemoryUsageFn;

      healthController.metrics(mockReq as Request, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(500);
      expect(mockRes.json).toHaveBeenCalledWith({
        success: false,
        error: {
          code: 'METRICS_ERROR',
          message: 'Failed to retrieve metrics',
          error: expect.any(Error)
        },
        timestamp: expect.any(Date)
      });

      // Restore original function
      process.memoryUsage = originalMemoryUsage;
    });
  });
});