/**
 * NLU Service Tests
 */

import axios from 'axios';
import { NLUService } from '../../services/nlu.service';

// Mock axios
jest.mock('axios');
const mockedAxios = axios as jest.Mocked<typeof axios>;

describe('NLUService', () => {
  let nluService: NLUService;
  let mockAxiosInstance: any;

  beforeEach(() => {
    mockAxiosInstance = {
      post: jest.fn().mockImplementation((url, data) => {
        return new Promise(resolve => setTimeout(() => {
          if (url === '/api/v1/process') {
            return resolve({ data: { data: { response: { intent: { name: 'greeting', confidence: 0.95 }, entities: [] } } } });
          } else if (url === '/api/v1/models/load') {
            return resolve({ data: { success: true } });
          }
          return resolve({});
        }, 10));
      }),
      get: jest.fn().mockImplementation((url) => {
        return new Promise(resolve => setTimeout(() => {
          if (url === '/health') {
            return resolve({ status: 200, data: { success: true } });
          }
          return resolve({});
        }, 10));
      }),
      interceptors: {
        request: {
          use: jest.fn()
        },
        response: {
          use: jest.fn()
        }
      }
    };

    mockedAxios.create.mockReturnValue(mockAxiosInstance);
    nluService = new NLUService();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('constructor', () => {
    it('should create axios instance with correct configuration', () => {
      expect(mockedAxios.create).toHaveBeenCalledWith({
        baseURL: expect.any(String),
        headers: {
          'Content-Type': 'application/json',
          Authorization: 'Bearer your-api-key-for-internal-services'
        }
      });
    });

    it('should setup interceptors', () => {
      expect(mockAxiosInstance.interceptors.request.use).toHaveBeenCalled();
      expect(mockAxiosInstance.interceptors.response.use).toHaveBeenCalled();
    });
  });

  describe('parseMessage', () => {
    const mockRequest = {
      message: 'Hello world',
      botId: 'bot-123'
    };

    it('should parse message successfully', async () => {
      const mockResponse = {
        data: {
          data: {
            response: {
              intent: {
                name: 'greeting',
                confidence: 0.95
              },
              entities: [
                {
                  entity: 'name',
                  value: 'world'
                }
              ]
            }
          }
        }
      };

      mockAxiosInstance.post.mockResolvedValue(mockResponse);

      const result = await nluService.parseMessage(mockRequest);

      expect(mockAxiosInstance.post).toHaveBeenCalledWith('/api/v1/process', mockRequest);
      expect(result).toEqual({
        intent: {
          name: 'greeting',
          confidence: 0.95
        },
        entities: [
          {
            entity: 'name',
            value: 'world'
          }
        ],
        text: 'Hello world'
      });
    });

    it('should handle missing intent gracefully', async () => {
      const mockResponse = {
        data: {
          data: {
            response: {
              entities: []
            }
          }
        }
      };

      mockAxiosInstance.post.mockResolvedValue(mockResponse);

      const result = await nluService.parseMessage(mockRequest);

      expect(result.intent).toEqual({
        name: 'nlu_fallback',
        confidence: 0
      });
    });

    it('should return fallback response on error', async () => {
      mockAxiosInstance.post.mockRejectedValue(new Error('NLU service error'));

      const result = await nluService.parseMessage(mockRequest);

      expect(result).toEqual({
        intent: {
          name: 'nlu_fallback',
          confidence: 0
        },
        entities: [],
        text: 'Hello world'
      });
    });
  });

  describe('loadModel', () => {
    const mockRequest = {
      botId: 'bot-123',
      modelPath: '/path/to/model'
    };

    it('should load model successfully', async () => {
      mockAxiosInstance.post.mockResolvedValue({ data: { success: true } });

      const result = await nluService.loadModel(mockRequest);

      expect(mockAxiosInstance.post).toHaveBeenCalledWith('/api/v1/models/load', {
        botId: 'bot-123',
        modelUrl: '/path/to/model'
      });
      expect(result).toEqual({ success: true });
    });

    it('should return failure response on error', async () => {
      mockAxiosInstance.post.mockRejectedValue(new Error('Load model error'));

      const result = await nluService.loadModel(mockRequest);

      expect(result).toEqual({ success: false });
    });
  });

  describe('isIntentConfident', () => {
    it('should return true for confident intent', () => {
      const intent = { name: 'greeting', confidence: 0.8 };
      const result = nluService.isIntentConfident(intent, 0.7);

      expect(result).toBe(true);
    });

    it('should return false for low confidence intent', () => {
      const intent = { name: 'greeting', confidence: 0.5 };
      const result = nluService.isIntentConfident(intent, 0.7);

      expect(result).toBe(false);
    });

    it('should return false for fallback intent', () => {
      const intent = { name: 'nlu_fallback', confidence: 0.9 };
      const result = nluService.isIntentConfident(intent, 0.7);

      expect(result).toBe(false);
    });

    it('should use default threshold', () => {
      const intent = { name: 'greeting', confidence: 0.8 };
      const result = nluService.isIntentConfident(intent);

      expect(result).toBe(true);
    });
  });

  describe('getFallbackIntent', () => {
    it('should return fallback intent', () => {
      const result = nluService.getFallbackIntent();

      expect(result).toEqual({
        name: 'nlu_fallback',
        confidence: 0
      });
    });
  });

  describe('healthCheck', () => {
    it('should return healthy status when service is up', async () => {
      mockAxiosInstance.get.mockResolvedValue({
        status: 200,
        data: { success: true }
      });

      const result = await nluService.healthCheck();

      expect(mockAxiosInstance.get).toHaveBeenCalledWith('/health');
      expect(result.status).toBe('healthy');
      expect(result.responseTime).toBeGreaterThan(0);
    });

    it('should return unhealthy status for non-200 response', async () => {
      mockAxiosInstance.get.mockResolvedValue({
        status: 500,
        data: { success: false }
      });

      const result = await nluService.healthCheck();

      expect(result.status).toBe('unhealthy');
      expect(result.error).toContain('Unexpected status');
    });

    it('should return unhealthy status for unsuccessful response', async () => {
      mockAxiosInstance.get.mockResolvedValue({
        status: 200,
        data: { success: false }
      });

      const result = await nluService.healthCheck();

      expect(result.status).toBe('unhealthy');
      expect(result.error).toContain('Unexpected status');
    });

    it('should return unhealthy status on error', async () => {
      const error = new Error('Connection failed');
      mockAxiosInstance.get.mockRejectedValue(error);

      const result = await nluService.healthCheck();

      expect(result.status).toBe('unhealthy');
      expect(result.error).toBe('Connection failed');
      expect(result.responseTime).toBeGreaterThan(0);
    });
  });

  describe('testConnection', () => {
    it('should return true when health check passes', async () => {
      mockAxiosInstance.get.mockResolvedValue({
        status: 200,
        data: { success: true }
      });

      const result = await nluService.testConnection();

      expect(result).toBe(true);
    });

    it('should return false when health check fails', async () => {
      mockAxiosInstance.get.mockRejectedValue(new Error('Connection failed'));

      const result = await nluService.testConnection();

      expect(result).toBe(false);
    });
  });
});