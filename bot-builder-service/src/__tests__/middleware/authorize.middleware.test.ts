import { Request, Response, NextFunction } from "express";
import { authorizeMiddleware } from "../../middleware/authorize.middleware";
import { errorResponse } from "@neuratalk/common";
import { PermissionKeys } from "../../types";

describe("authorizeMiddleware", () => {
  let mockRequest: Partial<Request>;
  let mockResponse: Partial<Response>;
  let nextFunction: jest.Mock<NextFunction>;

  beforeEach(() => {
    mockRequest = {
      user: {} as any, // Initialize user property
    };
    mockResponse = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn(),
    };
    nextFunction = jest.fn();
  });

  it("should call next() if user has all required permissions", () => {
    mockRequest.user = {
      id: "test-user",
      permissions: [PermissionKeys.CREATE_NEURATALK, PermissionKeys.READ_NEURATALK],
    };
    const requiredPermissions = [PermissionKeys.CREATE_NEURATALK, PermissionKeys.READ_NEURATALK];

    authorizeMiddleware(requiredPermissions)(
      mockRequest as Request,
      mockResponse as Response,
      nextFunction,
    );

    expect(nextFunction).toHaveBeenCalledTimes(1);
    expect(mockResponse.status).not.toHaveBeenCalled();
    expect(mockResponse.json).not.toHaveBeenCalled();
  });

  it("should return 403 if user does not have all required permissions", () => {
    mockRequest.user = {
      id: "test-user",
      permissions: [PermissionKeys.READ_NEURATALK],
    };
    const requiredPermissions = [PermissionKeys.CREATE_NEURATALK, PermissionKeys.READ_NEURATALK];

    authorizeMiddleware(requiredPermissions)(
      mockRequest as Request,
      mockResponse as Response,
      nextFunction,
    );

    expect(nextFunction).not.toHaveBeenCalled();
    expect(mockResponse.status).toHaveBeenCalledWith(403);
    expect(mockResponse.json).toHaveBeenCalledWith(
      errorResponse({
        code: "AUTH_ERROR",
        message: "Forbidden",
      }),
    );
  });

  it("should call next() if no specific permissions are required", () => {
    mockRequest.user = {
      id: "test-user",
      permissions: [PermissionKeys.READ_NEURATALK],
    };
    const requiredPermissions: PermissionKeys[] = [];

    authorizeMiddleware(requiredPermissions)(
      mockRequest as Request,
      mockResponse as Response,
      nextFunction,
    );

    expect(nextFunction).toHaveBeenCalledTimes(1);
    expect(mockResponse.status).not.toHaveBeenCalled();
    expect(mockResponse.json).not.toHaveBeenCalled();
  });

  it("should return 403 if req.user is missing", () => {
    (mockRequest as any).user = undefined; // Cast to any here
    const requiredPermissions = [PermissionKeys.READ_NEURATALK];

    authorizeMiddleware(requiredPermissions)(
      mockRequest as Request,
      mockResponse as Response,
      nextFunction,
    );

    expect(nextFunction).not.toHaveBeenCalled();
    expect(mockResponse.status).toHaveBeenCalledWith(403);
    expect(mockResponse.json).toHaveBeenCalledWith(
      errorResponse({
        code: "AUTH_ERROR",
        message: "Missing or invalid Authorization header",
      }),
    );
  });

  it("should handle errors during authorization", () => {
    const originalConsoleError = console.error;
    console.error = jest.fn();

    const requiredPermissions = [PermissionKeys.READ_NEURATALK];

    // Simulate an error by making `req.user.permissions` throw when accessed.
    Object.defineProperty(mockRequest, "user", {
      get: () => {
        throw new Error("Simulated user access error");
      },
    });

    authorizeMiddleware(requiredPermissions)(
      mockRequest as Request,
      mockResponse as Response,
      nextFunction,
    );

    expect(nextFunction).toHaveBeenCalledWith(expect.any(Error));
    expect(mockResponse.status).not.toHaveBeenCalled();
    expect(mockResponse.json).not.toHaveBeenCalled();

    console.error = originalConsoleError;
  });
});
