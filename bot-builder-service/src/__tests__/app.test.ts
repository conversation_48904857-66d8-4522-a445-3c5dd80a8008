import { App } from "../app";
import { ADMIN_USER_ID } from "../config";

// Mock external dependencies
jest.mock("jsonwebtoken", () => ({
  decode: jest.fn(() => ({
    sub: ADMIN_USER_ID,
    name: "Admin",
    preferred_username: "admin",
    scope: "neuratalk_c neuratalk_e neuratalk_d neuratalk_r",
    exp: Math.floor(Date.now() / 1000) + 60 * 60, // Token expires in 1 hour
  })),
}));

jest.mock("@neuratalk/bot-store", () => ({
  DatabaseConnection: jest.fn().mockImplementation(() => ({
    connect: jest.fn().mockResolvedValue(undefined),
    disconnect: jest.fn().mockResolvedValue(undefined),
    models: {},
  })),
}));

jest.mock("../services/bot.service", () => ({
  BotService: jest.fn().mockImplementation(() => ({})),
}));

jest.mock("../services/flow.service", () => ({
  FlowService: jest.fn().mockImplementation(() => ({})),
}));

jest.mock("../services/build.service", () => ({
  BuildService: jest.fn().mockImplementation(() => ({})),
}));

jest.mock("../services/build-sse.service", () => ({
  BuildSseService: {
    getInstance: jest.fn().mockReturnValue({
      addClient: jest.fn(),
      removeClient: jest.fn(),
      sendEvent: jest.fn(),
    }),
  },
}));

jest.mock("api_gw", () => ({
  initializeApiGw: jest.fn().mockResolvedValue(undefined),
}));

jest.mock("../routers/index.router", () => ({
  createRoutes: jest.fn().mockReturnValue(jest.fn()),
}));

jest.mock("@neuratalk/common", () => ({
  ...jest.requireActual("@neuratalk/common"),
  KafkaProducer: jest.fn().mockImplementation(() => ({
    connect: jest.fn().mockResolvedValue(undefined),
    disconnect: jest.fn().mockResolvedValue(undefined),
  })),
  KafkaConsumer: jest.fn().mockImplementation(() => ({
    connect: jest.fn().mockResolvedValue(undefined),
    disconnect: jest.fn().mockResolvedValue(undefined),
    subscribeAndRun: jest.fn().mockResolvedValue(undefined),
    handleMessage: jest.fn().mockResolvedValue(undefined),
  })),
}));

jest.mock("../middleware/authenticate.middleware", () => ({
  authenticateMiddleware: jest.fn(),
}));
jest.mock("../middleware/authorize.middleware", () => ({
  authorizeMiddleware: jest.fn(),
}));

describe("App", () => {
  let app: App;

  beforeEach(() => {
    app = new App();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it("should create an App instance", () => {
    expect(app).toBeInstanceOf(App);
    expect(app.app).toBeDefined();
  });

  it("should have express app property", () => {
    expect(app).toBeInstanceOf(App);
    expect(app.app).toBeDefined();
  });

  it("should start the application successfully", async () => {
    const mockListen = jest.spyOn(app.app, "listen").mockImplementation((port, callback) => {
      if (callback) callback();
      return {} as any; // Mock server object
    });

    await app.start();

    expect(mockListen).toHaveBeenCalled();
  });

  it("should handle startup errors", async () => {
    const mockExit = jest.spyOn(process, "exit").mockImplementation(() => {
      throw new Error("Process exit called");
    });

    // Mock a service initialization failure
    jest
      .spyOn(app as any, "initializeCoreServices")
      .mockRejectedValue(new Error("Service init failed"));

    await expect(app.start()).rejects.toThrow("Process exit called");
    expect(mockExit).toHaveBeenCalledWith(1);

    mockExit.mockRestore();
  });

  it("should stop the application successfully", async () => {
    // First start the app to initialize context
    const mockListen = jest.spyOn(app.app, "listen").mockImplementation((port, callback) => {
      if (callback) callback();
      return {} as any;
    });

    await app.start();
    await app.stop();

    // Verify that disconnect methods were called
    expect(app).toBeInstanceOf(App);
    mockListen.mockRestore();
  });

  it("should handle stop errors gracefully", async () => {
    // First start the app
    const mockListen = jest.spyOn(app.app, "listen").mockImplementation((port, callback) => {
      if (callback) callback();
      return {} as any;
    });

    await app.start();

    // Mock disconnect to throw an error
    const mockContext = (app as any).context;
    if (mockContext && mockContext.db) {
      mockContext.db.disconnect = jest.fn().mockRejectedValue(new Error("Disconnect failed"));
    }

    // Should not throw, just log the error
    await expect(app.stop()).resolves.not.toThrow();

    mockListen.mockRestore();
  });

  describe("Route endpoints", () => {
    let request: any;

    beforeEach(async () => {
      const mockListen = jest.spyOn(app.app, "listen").mockImplementation((port, callback) => {
        if (callback) callback();
        return {} as any;
      });

      await app.start();
      request = require("supertest")(app.app);
      mockListen.mockRestore();
    });

    it("should respond to health check endpoint", async () => {
      const response = await request.get("/health");

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.status).toBe("healthy");
      expect(response.body.data.service).toBe("bot-builder-service");
    });

    it("should respond to root endpoint", async () => {
      const response = await request.get("/");

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.service).toBe("bot-builder-service");
      expect(response.body.data.endpoints).toBeDefined();
    });

    it("should respond to swagger JSON endpoint", async () => {
      const response = await request.get("/api-docs.json");

      expect(response.status).toBe(200);
      expect(response.headers["content-type"]).toContain("application/json");
    });

    it("should handle 404 for unknown routes", async () => {
      const response = await request.get("/unknown-route");

      expect(response.status).toBe(404);
      expect(response.body.success).toBe(false);
      expect(response.body.error.code).toBe("NOT_FOUND");
      expect(response.body.error.message).toContain("Route GET /unknown-route not found");
    });

    it("should handle global errors", async () => {
      // Create a new app instance to add the error route before initialization
      const testApp = new App();
      const mockListen = jest.spyOn(testApp.app, "listen").mockImplementation((port, callback) => {
        if (callback) callback();
        return {} as any;
      });

      // Add error route before starting the app
      testApp.app.get("/test-error", () => {
        throw new Error("Test error");
      });

      await testApp.start();
      const testRequest = require("supertest")(testApp.app);

      const response = await testRequest.get("/test-error");

      expect(response.status).toBe(500);
      expect(response.body.success).toBe(false);
      expect(response.body.error.code).toBe("INTERNAL_ERROR");

      mockListen.mockRestore();
    });

    it("should add request ID to requests", async () => {
      const response = await request.get("/health");

      expect(response.headers["x-request-id"]).toBeDefined();
    });

    it("should use provided request ID from headers", async () => {
      const customRequestId = "custom-request-id-123";
      const response = await request.get("/health").set("x-request-id", customRequestId);

      expect(response.headers["x-request-id"]).toBe(customRequestId);
    });
  });
});
