import { App } from "../app";
import { ADMIN_USER_ID } from "../config";

// Mock external dependencies
jest.mock("jsonwebtoken", () => ({
  decode: jest.fn(() => ({
    sub: ADMIN_USER_ID,
    name: "Admin",
    preferred_username: "admin",
    scope: "neuratalk_c neuratalk_e neuratalk_d neuratalk_r",
    exp: Math.floor(Date.now() / 1000) + 60 * 60, // Token expires in 1 hour
  })),
}));

jest.mock("@neuratalk/bot-store", () => ({
  DatabaseConnection: jest.fn().mockImplementation(() => ({
    connect: jest.fn().mockResolvedValue(undefined),
    disconnect: jest.fn().mockResolvedValue(undefined),
    models: {},
  })),
}));

jest.mock("../services/bot.service", () => ({
  BotService: jest.fn().mockImplementation(() => ({})),
}));

jest.mock("../services/flow.service", () => ({
  FlowService: jest.fn().mockImplementation(() => ({})),
}));

jest.mock("../services/build.service", () => ({
  BuildService: jest.fn().mockImplementation(() => ({})),
}));

jest.mock("../services/build-sse.service", () => ({
  BuildSseService: {
    getInstance: jest.fn().mockReturnValue({
      addClient: jest.fn(),
      removeClient: jest.fn(),
      sendEvent: jest.fn(),
    }),
  },
}));

jest.mock("api_gw", () => ({
  initializeApiGw: jest.fn().mockResolvedValue(undefined),
}));

jest.mock("../routers/index.router", () => ({
  createRoutes: jest.fn().mockReturnValue(jest.fn()),
}));

jest.mock("@neuratalk/common", () => ({
  ...jest.requireActual("@neuratalk/common"),
  KafkaProducer: jest.fn().mockImplementation(() => ({
    connect: jest.fn().mockResolvedValue(undefined),
    disconnect: jest.fn().mockResolvedValue(undefined),
  })),
  KafkaConsumer: jest.fn().mockImplementation(() => ({
    connect: jest.fn().mockResolvedValue(undefined),
    disconnect: jest.fn().mockResolvedValue(undefined),
    subscribeAndRun: jest.fn().mockResolvedValue(undefined),
    handleMessage: jest.fn().mockResolvedValue(undefined),
  })),
}));

jest.mock("../middleware/authenticate.middleware", () => ({
  authenticateMiddleware: jest.fn(),
}));
jest.mock("../middleware/authorize.middleware", () => ({
  authorizeMiddleware: jest.fn(),
}));

describe("App", () => {
  let app: App;

  beforeEach(() => {
    app = new App();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it("should create an App instance", () => {
    expect(app).toBeInstanceOf(App);
    expect(app.app).toBeDefined();
  });

  it("should have express app property", () => {
    expect(app).toBeInstanceOf(App);
    expect(app.app).toBeDefined();
  });

  it("should start the application successfully", async () => {
    const mockListen = jest.spyOn(app.app, "listen").mockImplementation((port, callback) => {
      if (callback) callback();
      return {} as any; // Mock server object
    });

    await app.start();

    expect(mockListen).toHaveBeenCalled();
  });
});
