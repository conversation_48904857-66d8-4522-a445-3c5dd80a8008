
import { Op } from "sequelize";

import { FlowService } from "../../services/flow.service";
import { DatabaseConnection, Models, FlowType } from "@neuratalk/bot-store";
import { logger } from "@neuratalk/common";
import { getStudioAppsService } from "api_gw";

jest.mock("@neuratalk/common", () => ({
  ...jest.requireActual("@neuratalk/common"),
  logger: {
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn(),
  },
}));

jest.mock("api_gw", () => ({
  getStudioAppsService: jest.fn().mockReturnValue({
    createApp: jest.fn(),
    exportApp: jest.fn((appId) => ({
      id: appId,
      name: "Mock App",
      ngage_id: "mock-ngage-id",
    })),
  }),
}));

describe("FlowService", () => {
  let flowService: FlowService;
  let mockDb: jest.Mocked<DatabaseConnection>;
  let mockModels: jest.Mocked<Models>;
  let mockStudioAppsService: any;

  beforeEach(() => {
    mockModels = {
      Flow: {
        create: jest.fn(),
        findByPk: jest.fn(),
        update: jest.fn(),
        destroy: jest.fn(),
        findAll: jest.fn(),
        findAndCountAll: jest.fn(),
      },
    } as unknown as jest.Mocked<Models>;

    mockDb = {
      models: mockModels,
      transaction: jest.fn((callback) => callback({})) as any,
      sequelize: {
        transaction: jest.fn(() => ({
          commit: jest.fn().mockResolvedValue(undefined),
          rollback: jest.fn().mockResolvedValue(undefined),
        })),
      } as any,
    } as unknown as jest.Mocked<DatabaseConnection>;

    mockStudioAppsService = getStudioAppsService();
    mockStudioAppsService.createApp.mockResolvedValue({
      id: "app-123",
      name: "Mock App",
      ngage_id: "mock-ngage-id",
    });
    mockStudioAppsService.exportApp.mockImplementation((req: any, res: any) => {
      // Mock implementation for exportApp
      // It expects req and res, so we need to handle them
      if (req && req.params && req.params.appId) {
        return {
          id: req.params.appId,
          name: "Mock App",
          ngage_id: "mock-ngage-id",
        };
      }
      return null;
    });

    flowService = new FlowService(mockDb);

    jest.clearAllMocks();
  });

  describe("createFlow", () => {
    const mockFlowRequest = {
      name: "Test Flow",
      description: "A flow for testing",
      botId: "bot-123",
      type: FlowType.CUSTOM,
    };
    const mockUserId = "user-123";
    const mockFlowId = "flow-123";

    it("should create a flow successfully", async () => {
      const mockFlowInstance = {
        id: mockFlowId,
        ...mockFlowRequest,
        toJSON: () => ({ id: mockFlowId, ...mockFlowRequest }), // This is what the service returns
      };
      (mockModels.Flow.create as jest.Mock).mockResolvedValue(mockFlowInstance);

      const result = await flowService.createFlow(mockFlowRequest, mockUserId);

      expect(mockDb.transaction).toHaveBeenCalled();
      expect(mockStudioAppsService.createApp).toHaveBeenCalled();
      expect(mockModels.Flow.create).toHaveBeenCalledWith(
        expect.objectContaining({
          name: mockFlowRequest.name,
          botId: mockFlowRequest.botId,
          appId: "app-123",
          createdBy: mockUserId,
          updatedBy: mockUserId,
        }),
        expect.any(Object), // Keep this as expect.any(Object) for transaction
      );
      expect(logger.info).toHaveBeenCalledWith(`Flow created: ${mockFlowId} - ${mockFlowRequest.name}`);
      expect(result).toEqual(mockFlowInstance.toJSON()); // Expect the plain object
    });

    it("should create a flow successfully within an existing transaction", async () => {
      const mockTransaction = {
        commit: jest.fn(),
        rollback: jest.fn(),
      };
      (mockDb.transaction as jest.Mock).mockImplementationOnce(async (callback) => {
        return callback(mockTransaction);
      });

      const mockFlowInstance = {
        id: mockFlowId,
        ...mockFlowRequest,
        toJSON: () => ({ id: mockFlowId, ...mockFlowRequest }),
      };
      (mockModels.Flow.create as jest.Mock).mockResolvedValue(mockFlowInstance);

      const result = await flowService.createFlow(mockFlowRequest, mockUserId, mockTransaction as any);

      expect(mockDb.transaction).not.toHaveBeenCalled(); // Should not start a new transaction
      expect(mockStudioAppsService.createApp).toHaveBeenCalled();
      expect(mockModels.Flow.create).toHaveBeenCalledWith(
        expect.objectContaining({
          name: mockFlowRequest.name,
          botId: mockFlowRequest.botId,
          appId: "app-123",
          createdBy: mockUserId,
          updatedBy: mockUserId,
        }),
        expect.any(Object), // Expect the specific mockTransaction object
      );
      expect(result).toEqual(mockFlowInstance.toJSON()); // Expect the plain object
    });
  });

  describe("getFlowById", () => {
    const mockFlowId = "flow-123";
    const mockFlow = { id: mockFlowId, name: "Test Flow", toJSON: () => ({ id: mockFlowId, name: "Test Flow" }) };

    it("should retrieve a flow by ID", async () => {
      (mockModels.Flow.findByPk as jest.Mock).mockResolvedValue(mockFlow);

      const result = await flowService.getFlowById(mockFlowId);

      expect(mockModels.Flow.findByPk).toHaveBeenCalledWith(mockFlowId);
      expect(result).toEqual(mockFlow.toJSON());
    });

    it("should return null if flow not found", async () => {
      (mockModels.Flow.findByPk as jest.Mock).mockResolvedValue(null);

      const result = await flowService.getFlowById("non-existent-id");

      expect(mockModels.Flow.findByPk).toHaveBeenCalledWith("non-existent-id");
      expect(result).toBeNull();
    });

    it("should handle errors during retrieval", async () => {
      const error = new Error("Database error");
      (mockModels.Flow.findByPk as jest.Mock).mockRejectedValue(error);

      await expect(flowService.getFlowById(mockFlowId)).rejects.toThrow(error);
      expect(logger.error).toHaveBeenCalledWith(`Error getting flow ${mockFlowId}:`, error);
    });
  });

  describe("updateFlow", () => {
    const mockFlowId = "flow-123";
    const mockUpdateData = { name: "Updated Flow Name" };
    const mockUserId = "user-123";

    it("should update flow properties successfully", async () => {
      const existingFlow = {
        id: mockFlowId,
        name: "Original Flow",
        update: jest.fn().mockResolvedValue(true),
        toJSON: () => ({ id: mockFlowId, ...mockUpdateData }),
      };
      (mockModels.Flow.findByPk as jest.Mock).mockResolvedValue(existingFlow);

      const result = await flowService.updateFlow(mockFlowId, mockUpdateData, mockUserId);

      expect(mockModels.Flow.findByPk).toHaveBeenCalledWith(mockFlowId);
      expect(existingFlow.update).toHaveBeenCalledWith(
        expect.objectContaining({
          name: mockUpdateData.name,
          updatedBy: mockUserId,
        }),
      );
      expect(result).toEqual(existingFlow.toJSON());
    });

    it("should return null if flow not found during update", async () => {
      (mockModels.Flow.findByPk as jest.Mock).mockResolvedValue(null);

      const result = await flowService.updateFlow("non-existent-id", mockUpdateData, mockUserId);

      expect(mockModels.Flow.findByPk).toHaveBeenCalledWith("non-existent-id");
      expect(result).toBeNull();
    });

    it("should handle errors during update", async () => {
      const error = new Error("Database error");
      (mockModels.Flow.findByPk as jest.Mock).mockRejectedValue(error);

      await expect(flowService.updateFlow(mockFlowId, mockUpdateData, mockUserId)).rejects.toThrow(error);
      expect(logger.error).toHaveBeenCalledWith(`Error updating flow ${mockFlowId}:`, error);
    });
  });

  describe("deleteFlow", () => {
    const mockFlowId = "flow-123";

    it("should delete a flow successfully", async () => {
      (mockModels.Flow.destroy as jest.Mock).mockResolvedValue(1);

      const result = await flowService.deleteFlow(mockFlowId);

      expect(mockModels.Flow.destroy).toHaveBeenCalledWith({ where: { id: mockFlowId, type: FlowType.CUSTOM } });
      expect(logger.info).toHaveBeenCalledWith(`Flow deleted: ${mockFlowId}`);
      expect(result).toBe(true);
    });

    it("should return false if flow not found during deletion", async () => {
      (mockModels.Flow.destroy as jest.Mock).mockResolvedValue(0);

      const result = await flowService.deleteFlow("non-existent-id");

      expect(mockModels.Flow.destroy).toHaveBeenCalledWith({ where: { id: "non-existent-id", type: FlowType.CUSTOM } });
      expect(logger.info).not.toHaveBeenCalledWith(`Flow deleted: non-existent-id`);
      expect(result).toBe(false);
    });

    it("should handle errors during deletion", async () => {
      const error = new Error("Database error");
      (mockModels.Flow.destroy as jest.Mock).mockRejectedValue(error);

      await expect(flowService.deleteFlow(mockFlowId)).rejects.toThrow(error);
      expect(logger.error).toHaveBeenCalledWith(`Error deleting flow ${mockFlowId}:`, error);
    });
  });

  describe("getFlowsByBotId", () => {
    const mockBotId = "bot-123";
    const mockFlows = [
      { id: "flow-1", name: "Flow One", toJSON: () => ({ id: "flow-1", name: "Flow One" }) },
      { id: "flow-2", name: "Flow Two", toJSON: () => ({ id: "flow-2", name: "Flow Two" }) },
    ];

    it("should retrieve all flows for a given bot ID", async () => {
      (mockModels.Flow.findAll as jest.Mock).mockResolvedValue(mockFlows);

      const result = await flowService.getFlowsByBotId(mockBotId);

      expect(mockModels.Flow.findAll).toHaveBeenCalledWith({ where: { botId: mockBotId } });
      expect(result).toEqual(mockFlows.map(f => f.toJSON()));
    });

    it("should handle errors during retrieval", async () => {
      const error = new Error("Database error");
      (mockModels.Flow.findAll as jest.Mock).mockRejectedValue(error);

      await expect(flowService.getFlowsByBotId(mockBotId)).rejects.toThrow(error);
      expect(logger.error).toHaveBeenCalledWith(`Error getting all flows for bot ${mockBotId}:`, error);
    });
  });

  describe("getFlowsByBot", () => {
    const mockBotId = "bot-123";
    const mockFlows = [
      { id: "flow-1", name: "Flow One", toJSON: () => ({ id: "flow-1", name: "Flow One" }) },
      { id: "flow-2", name: "Flow Two", toJSON: () => ({ id: "flow-2", name: "Flow Two" }) },
    ];

    it("should retrieve paginated flows for a given bot ID", async () => {
      (mockModels.Flow.findAndCountAll as jest.Mock).mockResolvedValue({
        count: mockFlows.length,
        rows: mockFlows,
      });

      const result = await flowService.getFlowsByBot(mockBotId, 1, 20);

      expect(mockModels.Flow.findAndCountAll).toHaveBeenCalledWith(
        expect.objectContaining({
          where: { botId: mockBotId },
          limit: 20,
          offset: 0,
        }),
      );
      expect(result).toEqual({ flows: mockFlows.map(f => f.toJSON()), total: mockFlows.length });
    });

    it("should retrieve paginated flows with search term", async () => {
      (mockModels.Flow.findAndCountAll as jest.Mock).mockResolvedValue({
        count: 1,
        rows: [mockFlows[0]],
      });

      const result = await flowService.getFlowsByBot(mockBotId, 1, 20, "Flow One");

      expect(mockModels.Flow.findAndCountAll).toHaveBeenCalledWith(
        expect.objectContaining({
          where: {
            botId: mockBotId,
            [Op.or]: [
              { name: { [Op.like]: `%Flow One%` } },
              { description: { [Op.like]: `%Flow One%` } },
            ],
          },
          limit: 20,
          offset: 0,
          order: [["createdAt", "DESC"]], // Add order expectation
        }),
      );
      expect(result).toEqual({ flows: [mockFlows[0].toJSON()], total: 1 });
    });

    it("should retrieve paginated flows with isActive filter", async () => {
      (mockModels.Flow.findAndCountAll as jest.Mock).mockResolvedValue({
        count: 1,
        rows: [mockFlows[0]],
      });

      const result = await flowService.getFlowsByBot(mockBotId, 1, 20, undefined, true);

      expect(mockModels.Flow.findAndCountAll).toHaveBeenCalledWith(
        expect.objectContaining({
          where: { botId: mockBotId, isActive: true },
          limit: 20,
          offset: 0,
        }),
      );
      expect(result).toEqual({ flows: [mockFlows[0].toJSON()], total: 1 });
    });

    it("should handle errors during paginated retrieval", async () => {
      const error = new Error("Database error");
      (mockModels.Flow.findAndCountAll as jest.Mock).mockRejectedValue(error);

      await expect(flowService.getFlowsByBot(mockBotId, 1, 20)).rejects.toThrow(error);
      expect(logger.error).toHaveBeenCalledWith(`Error getting flows for bot ${mockBotId}:`, error);
    });
  });

  describe("cloneFlow", () => {
    const mockOriginalFlowId = "original-flow-123";
    const mockUserId = "user-123";
    const mockOriginalFlow = {
      id: mockOriginalFlowId,
      name: "Original Flow",
      appId: "original-app-123",
      toJSON: () => ({ id: mockOriginalFlowId, name: "Original Flow", appId: "original-app-123" }),
    };
    const mockExportedApp = {
      name: "Original App",
      ngage_id: "original-ngage-id",
    };
    const mockNewApp = {
      id: "new-app-123",
      name: "New App",
      ngage_id: "new-ngage-id",
    };
    const mockNewFlow = {
      id: "new-flow-123",
      name: "Original Flow - Clone",
      appId: "new-app-123",
      toJSON: () => ({ id: "new-flow-123", name: "Original Flow - Clone", appId: "new-app-123" }),
    };

    beforeEach(() => {
      (mockModels.Flow.findByPk as jest.Mock).mockResolvedValue(mockOriginalFlow);
      mockStudioAppsService.exportApp.mockResolvedValue(mockExportedApp);
      mockStudioAppsService.createApp.mockResolvedValue(mockNewApp);
      (mockModels.Flow.create as jest.Mock).mockResolvedValue(mockNewFlow);
    });

    it("should clone a flow successfully", async () => {
      const result = await flowService.cloneFlow(mockOriginalFlowId, mockUserId);

      expect(mockModels.Flow.findByPk).toHaveBeenCalledWith(mockOriginalFlowId, expect.any(Object));
      expect(mockStudioAppsService.exportApp).toHaveBeenCalledWith({
        params: {
          appId: mockOriginalFlow.appId,
        },
      }, null);
      expect(mockStudioAppsService.createApp).toHaveBeenCalledWith(
        expect.objectContaining({
          name: expect.stringContaining("Original App"),
          ngage_id: expect.any(String),
          type: "Live",
        }),
      );
      expect(mockModels.Flow.create).toHaveBeenCalledWith(
        expect.objectContaining({
          name: "Original Flow - Clone",
          appId: mockNewApp.id,
          createdBy: mockUserId,
          updatedBy: mockUserId,
        }),
        expect.any(Object),
      );
      expect(logger.info).toHaveBeenCalledWith(`Flow ${mockOriginalFlowId} successfully cloned to new flow ${mockNewFlow.id}`);
      expect(result).toEqual(mockNewFlow.toJSON());
    });

    it("should return null if original flow not found", async () => {
      (mockModels.Flow.findByPk as jest.Mock).mockResolvedValue(null);
      const result = await flowService.cloneFlow("non-existent-flow", mockUserId);
      expect(result).toBeNull();
    });

    it("should handle errors during cloning", async () => {
      const error = new Error("Cloning failed");
      (mockModels.Flow.findByPk as jest.Mock).mockRejectedValue(error);

      await expect(flowService.cloneFlow(mockOriginalFlowId, mockUserId)).rejects.toThrow(error);
      expect(logger.error).toHaveBeenCalledWith(`Error cloning flow ${mockOriginalFlowId}:`, error);
    });
  });
});
