import { Request, Response } from "express";
import { BotController } from "../controllers/bot.controller";
import { BotService } from "../services/bot.service";
import { Models } from "@neuratalk/bot-store";
import { AppContext } from "../types/context.types";

jest.mock("../services/bot.service");
jest.mock("@neuratalk/common", () => ({
  getPaginatedResults: jest.fn(),
  logger: {
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn(),
  },
  successResponse: jest.fn((data) => ({ success: true, data, timestamp: new Date() })),
  errorResponse: jest.fn((error) => ({
    success: false,
    error: { code: error.code, message: error.message },
    timestamp: new Date(),
  })),
}));

const { getPaginatedResults, successResponse, errorResponse } = require("@neuratalk/common");

describe("BotController", () => {
  let controller: BotController;
  let mockBotService: jest.Mocked<BotService>;
  let mockModels: Models;
  let mockReq: Partial<Request>;
  let mockRes: Partial<Response>;
  let mockContext: AppContext;

  beforeEach(() => {
    mockBotService = new BotService({} as any, {} as any) as jest.Mocked<BotService>;
    mockModels = {
      Bot: {} as unknown as Models["Bot"],
      BotLanguage: {} as unknown as Models["BotLanguage"],
      Language: {} as unknown as Models["Language"],
      FaqCategory: {} as unknown as Models["FaqCategory"],
      FaqItems: {} as unknown as Models["FaqItems"],
      FaqTranslation: {} as unknown as Models["FaqTranslation"],
      Flow: {} as unknown as Models["Flow"],
      IntentItems: {} as unknown as Models["IntentItems"],
      IntentUtterance: {} as unknown as Models["IntentUtterance"],
      UtteranceTranslation: {} as unknown as Models["UtteranceTranslation"],
      Entities: {} as unknown as Models["Entities"],
    } as unknown as Models;

    mockContext = {
      db: {
        models: mockModels,
        getSequelize: jest.fn(),
        connect: jest.fn(),
        disconnect: jest.fn(),
        transaction: jest.fn((cb) => cb({} as any)),
        healthCheck: jest.fn(),
      } as any,
      botService: mockBotService,
      flowService: {} as any,
      buildService: {} as any,
      buildSseService: {} as any,
    };

    controller = new BotController(mockContext);

    mockReq = {
      body: {},
      params: {},
      query: {},
      user: { id: "user-123" },
    };

    mockRes = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn().mockReturnThis(),
      send: jest.fn().mockReturnThis(),
    };

    jest.clearAllMocks();
  });

  describe("createBot", () => {
    it("should create a bot successfully", async () => {
      const mockBot = { id: "bot-123", name: "Test Bot" };
      mockReq.body = { name: "Test Bot" };
      mockBotService.createBot.mockResolvedValue(mockBot as any);

      await controller.createBot(mockReq as any, mockRes as Response);

      expect(mockBotService.createBot).toHaveBeenCalledWith(mockReq.body, "user-123");
      expect(mockRes.status).toHaveBeenCalledWith(201);
      expect(successResponse).toHaveBeenCalledWith(mockBot);
    });

    it("should handle creation error", async () => {
      mockReq.body = { name: "Test Bot" };
      mockBotService.createBot.mockRejectedValue(new Error("Creation failed"));

      await controller.createBot(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(500);
      expect(errorResponse).toHaveBeenCalledWith({
        error: expect.any(Error),
        code: "INTERNAL_ERROR",
        message: "Failed to create bot",
      });
    });
  });

  describe("getBotById", () => {
    it("should retrieve a bot by ID", async () => {
      const mockBot = { id: "bot-123", name: "Test Bot" };
      mockReq.params = { botId: "bot-123" };
      mockBotService.getBotById.mockResolvedValue(mockBot as any);

      await controller.getBotById(mockReq as any, mockRes as Response);

      expect(mockBotService.getBotById).toHaveBeenCalledWith("bot-123");
      expect(successResponse).toHaveBeenCalledWith(mockBot);
    });

    it("should return 404 if bot not found", async () => {
      mockReq.params = { botId: "non-existent" };
      mockBotService.getBotById.mockResolvedValue(null);

      await controller.getBotById(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(404);
      expect(errorResponse).toHaveBeenCalledWith({
        code: "BOT_NOT_FOUND",
        message: "Bot not found",
      });
    });

    it("should handle getById error", async () => {
      mockReq.params = { botId: "bot-123" };
      mockBotService.getBotById.mockRejectedValue(new Error("Database error"));

      await controller.getBotById(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(500);
      expect(errorResponse).toHaveBeenCalledWith({
        error: expect.any(Error),
        code: "INTERNAL_ERROR",
        message: "Failed to get bot",
      });
    });
  });

  describe("updateBot", () => {
    it("should update a bot successfully", async () => {
      const mockBot = { id: "bot-123", name: "Updated Bot" };
      mockReq.params = { botId: "bot-123" };
      mockReq.body = { name: "Updated Bot" };
      mockBotService.updateBot.mockResolvedValue(mockBot as any);

      await controller.updateBot(mockReq as any, mockRes as Response);

      expect(mockBotService.updateBot).toHaveBeenCalledWith("bot-123", mockReq.body, "user-123");
      expect(successResponse).toHaveBeenCalledWith(mockBot);
    });

    it("should return 404 if bot not found during update", async () => {
      mockReq.params = { botId: "non-existent" };
      mockReq.body = { name: "Updated Bot" };
      mockBotService.updateBot.mockResolvedValue(null);

      await controller.updateBot(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(404);
      expect(errorResponse).toHaveBeenCalledWith({
        code: "BOT_NOT_FOUND",
        message: "Bot not found",
      });
    });

    it("should handle update error", async () => {
      mockReq.params = { botId: "bot-123" };
      mockReq.body = { name: "Updated Bot" };
      mockBotService.updateBot.mockRejectedValue(new Error("Update failed"));

      await controller.updateBot(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(500);
      expect(errorResponse).toHaveBeenCalledWith({
        error: expect.any(Error),
        code: "INTERNAL_ERROR",
        message: "Failed to update bot",
      });
    });
  });

  describe("deleteBot", () => {
    it("should delete a bot successfully", async () => {
      mockReq.params = { botId: "bot-123" };
      mockBotService.deleteBot.mockResolvedValue(true);

      await controller.deleteBot(mockReq as any, mockRes as Response);

      expect(mockBotService.deleteBot).toHaveBeenCalledWith("bot-123");
      expect(mockRes.status).toHaveBeenCalledWith(204);
      expect(mockRes.send).toHaveBeenCalled();
    });

    it("should return 404 if bot not found during delete", async () => {
      mockReq.params = { botId: "non-existent" };
      mockBotService.deleteBot.mockResolvedValue(false);

      await controller.deleteBot(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(404);
      expect(errorResponse).toHaveBeenCalledWith({
        code: "BOT_NOT_FOUND",
        message: "Bot not found",
      });
    });

    it("should handle delete error", async () => {
      mockReq.params = { botId: "bot-123" };
      mockBotService.deleteBot.mockRejectedValue(new Error("Delete failed"));

      await controller.deleteBot(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(500);
      expect(errorResponse).toHaveBeenCalledWith({
        error: expect.any(Error),
        code: "INTERNAL_ERROR",
        message: "Failed to delete bot",
      });
    });
  });

  describe("getBots", () => {
    it("should retrieve all bots with pagination", async () => {
      const mockResult = {
        items: [{ id: "bot-1" }],
        pagination: { page: 1, limit: 20, total: 1, totalPages: 1, hasNext: false, hasPrev: false },
      };
      (getPaginatedResults as jest.Mock).mockResolvedValue(mockResult);

      await controller.getBots(mockReq as any, mockRes as Response);

      expect(getPaginatedResults).toHaveBeenCalledWith(mockModels.Bot, mockReq.query, ["name"]);
      expect(successResponse).toHaveBeenCalledWith(mockResult);
    });

    it("should handle getBots error", async () => {
      (getPaginatedResults as jest.Mock).mockRejectedValue(new Error("Database error"));

      await controller.getBots(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(500);
      expect(errorResponse).toHaveBeenCalledWith({
        error: expect.any(Error),
        code: "INTERNAL_ERROR",
        message: "Failed to fetch Bots",
      });
    });
  });

  describe("activateBot", () => {
    it("should activate a bot successfully", async () => {
      const mockBot = { id: "bot-123", status: "active" };
      mockReq.params = { botId: "bot-123" };
      mockBotService.activateBot.mockResolvedValue(mockBot as any);

      await controller.activateBot(mockReq as any, mockRes as Response);

      expect(mockBotService.activateBot).toHaveBeenCalledWith("bot-123", "user-123");
      expect(successResponse).toHaveBeenCalledWith(mockBot);
    });

    it("should return 404 if bot not found during activation", async () => {
      mockReq.params = { botId: "non-existent" };
      mockBotService.activateBot.mockResolvedValue(null);

      await controller.activateBot(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(404);
      expect(errorResponse).toHaveBeenCalledWith({
        code: "BOT_NOT_FOUND",
        message: "Bot not found",
      });
    });

    it("should handle activateBot error", async () => {
      mockReq.params = { botId: "bot-123" };
      mockBotService.activateBot.mockRejectedValue(new Error("Activation failed"));

      await controller.activateBot(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(500);
      expect(errorResponse).toHaveBeenCalledWith({
        error: expect.any(Error),
        code: "INTERNAL_ERROR",
        message: "Failed to activate bot",
      });
    });
  });

  describe("deactivateBot", () => {
    it("should deactivate a bot successfully", async () => {
      const mockBot = { id: "bot-123", status: "inactive" };
      mockReq.params = { botId: "bot-123" };
      mockBotService.deactivateBot.mockResolvedValue(mockBot as any);

      await controller.deactivateBot(mockReq as any, mockRes as Response);

      expect(mockBotService.deactivateBot).toHaveBeenCalledWith("bot-123", "user-123");
      expect(successResponse).toHaveBeenCalledWith(mockBot);
    });

    it("should return 404 if bot not found during deactivation", async () => {
      mockReq.params = { botId: "non-existent" };
      mockBotService.deactivateBot.mockResolvedValue(null);

      await controller.deactivateBot(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(404);
      expect(errorResponse).toHaveBeenCalledWith({
        code: "BOT_NOT_FOUND",
        message: "Bot not found",
      });
    });

    it("should handle deactivateBot error", async () => {
      mockReq.params = { botId: "bot-123" };
      mockBotService.deactivateBot.mockRejectedValue(new Error("Deactivation failed"));

      await controller.deactivateBot(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(500);
      expect(errorResponse).toHaveBeenCalledWith({
        error: expect.any(Error),
        code: "INTERNAL_ERROR",
        message: "Failed to deactivate bot",
      });
    });
  });

  describe("publishBot", () => {
    it("should publish a bot successfully", async () => {
      const mockResult = { message: "Bot published successfully with model ID model-123." };
      mockReq.params = { botId: "bot-123" };
      mockBotService.publishBot.mockResolvedValue(mockResult);

      await controller.publishBot(mockReq as any, mockRes as Response);

      expect(mockBotService.publishBot).toHaveBeenCalledWith("bot-123");
      expect(successResponse).toHaveBeenCalledWith(mockResult);
    });

    it("should return 404 if bot not found during publish", async () => {
      mockReq.params = { botId: "non-existent" };
      mockBotService.publishBot.mockRejectedValue(new Error("Bot not found."));

      await controller.publishBot(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(404);
      expect(errorResponse).toHaveBeenCalledWith({
        code: "NOT_FOUND",
        message: "Bot not found.",
      });
    });

    it("should return 404 if no built bot model exists during publish", async () => {
      mockReq.params = { botId: "bot-123" };
      mockBotService.publishBot.mockRejectedValue(
        new Error("No built bot model found for this bot."),
      );

      await controller.publishBot(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(404);
      expect(errorResponse).toHaveBeenCalledWith({
        code: "NOT_FOUND",
        message: "No built bot model found for this bot.",
      });
    });

    it("should handle generic publishBot error", async () => {
      mockReq.params = { botId: "bot-123" };
      mockBotService.publishBot.mockRejectedValue(new Error("Publish failed"));

      await controller.publishBot(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(500);
      expect(errorResponse).toHaveBeenCalledWith({
        error: expect.any(Error),
        code: "INTERNAL_ERROR",
        message: "Failed to publish bot",
      });
    });
  });

  describe("getChannelConfig", () => {
    it("should retrieve channel configuration successfully", async () => {
      const mockConfig = { channelType: "web", isActive: true };
      mockReq.params = { botId: "bot-123", channelType: "web" };
      mockBotService.getChannelConfig.mockResolvedValue(mockConfig);

      await controller.getChannelConfig(mockReq as any, mockRes as Response);

      expect(mockBotService.getChannelConfig).toHaveBeenCalledWith("bot-123", "web");
      expect(successResponse).toHaveBeenCalledWith(mockConfig);
    });

    it("should handle getChannelConfig error", async () => {
      mockReq.params = { botId: "bot-123", channelType: "web" };
      mockBotService.getChannelConfig.mockRejectedValue(new Error("Failed to get config"));

      await controller.getChannelConfig(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(500);
      expect(errorResponse).toHaveBeenCalledWith({
        error: expect.any(Error),
        code: "GET_CHANNEL_CONFIG_ERROR",
        message: "Failed to get channel configuration",
      });
    });
  });

  describe("cloneBot", () => {
    it("should clone a bot successfully", async () => {
      const mockBot = { id: "bot-123", name: "Cloned Bot" };
      mockReq.params = { botId: "bot-123" };
      mockBotService.cloneBot.mockResolvedValue(mockBot as any);

      await controller.cloneBot(mockReq as any, mockRes as Response);

      expect(mockBotService.cloneBot).toHaveBeenCalledWith("bot-123", "user-123");
      expect(successResponse).toHaveBeenCalledWith(mockBot);
    });

    it("should return 404 if bot not found during clone", async () => {
      mockReq.params = { botId: "non-existent" };
      mockBotService.cloneBot.mockResolvedValue(undefined as any);

      await controller.cloneBot(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(404);
      expect(errorResponse).toHaveBeenCalledWith({
        code: "BOT_NOT_FOUND",
        message: "Bot not found",
      });
    });

    it("should handle cloneBot error", async () => {
      mockReq.params = { botId: "bot-123" };
      mockBotService.cloneBot.mockRejectedValue(new Error("Clone failed"));

      await controller.cloneBot(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(500);
      expect(errorResponse).toHaveBeenCalledWith({
        error: expect.any(Error),
        code: "INTERNAL_ERROR",
        message: "Failed to clone bot",
      });
    });
  });

  describe("createChannelIntegration", () => {
    it("should create channel integration successfully", async () => {
      const mockIntegration = { id: "int-123", channelType: "web" };
      mockReq.params = { botId: "bot-123" };
      mockReq.body = { channelType: "web", config: {} };
      mockBotService.createChannelIntegration.mockResolvedValue(mockIntegration);

      await controller.createChannelIntegration(mockReq as any, mockRes as Response);

      expect(mockBotService.createChannelIntegration).toHaveBeenCalledWith("bot-123", mockReq.body);
      expect(mockRes.status).toHaveBeenCalledWith(201);
      expect(successResponse).toHaveBeenCalledWith(mockIntegration);
    });

    it("should handle createChannelIntegration error", async () => {
      mockReq.params = { botId: "bot-123" };
      mockReq.body = { channelType: "web", config: {} };
      mockBotService.createChannelIntegration.mockRejectedValue(new Error("Creation failed"));

      await controller.createChannelIntegration(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(500);
      expect(errorResponse).toHaveBeenCalledWith({
        error: expect.any(Error),
        code: "CREATE_CHANNEL_ERROR",
        message: "Failed to create channel integration",
      });
    });
  });

  describe("updateChannelIntegration", () => {
    it("should update channel integration successfully", async () => {
      const mockIntegration = { id: "int-123", channelType: "web", config: { key: "value" } };
      mockReq.params = { botId: "bot-123", channelId: "int-123" };
      mockReq.body = { config: { key: "value" } };
      mockBotService.updateChannelIntegration.mockResolvedValue(mockIntegration);

      await controller.updateChannelIntegration(mockReq as any, mockRes as Response);

      expect(mockBotService.updateChannelIntegration).toHaveBeenCalledWith(
        "bot-123",
        "int-123",
        mockReq.body,
      );
      expect(successResponse).toHaveBeenCalledWith(mockIntegration);
    });

    it("should handle updateChannelIntegration error", async () => {
      mockReq.params = { botId: "bot-123", channelId: "int-123" };
      mockReq.body = { config: { key: "value" } };
      mockBotService.updateChannelIntegration.mockRejectedValue(new Error("Update failed"));

      await controller.updateChannelIntegration(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(500);
      expect(errorResponse).toHaveBeenCalledWith({
        error: expect.any(Error),
        code: "UPDATE_CHANNEL_ERROR",
        message: "Failed to update channel integration",
      });
    });
  });
});
