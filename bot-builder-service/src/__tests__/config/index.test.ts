import config, { ADMIN_USER_ID } from "../config";

describe("Configuration", () => {
  const originalEnv = process.env;

  beforeEach(() => {
    jest.resetModules(); // Clear the cache for config module
    process.env = { ...originalEnv }; // Make a copy of the original env
  });

  afterAll(() => {
    process.env = originalEnv; // Restore original env
  });

  it("should load default server configuration", () => {
    expect(config.server.port).toBe(3000);
    expect(config.server.env).toBe("development");
    expect(config.server.corsOrigins).toEqual(["http://localhost:3000"]);
  });

  it("should load server configuration from environment variables", () => {
    process.env.PORT = "4000";
    process.env.NODE_ENV = "production";
    process.env.CORS_ORIGINS = "http://localhost:8080,http://localhost:8081";

    const newConfig = require("../../src/config").default;

    expect(newConfig.server.port).toBe(4000);
    expect(newConfig.server.env).toBe("production");
    expect(newConfig.server.corsOrigins).toEqual(["http://localhost:8080", "http://localhost:8081"]);
  });

  it("should load default database configuration", () => {
    expect(config.database.url).toBe("postgresql://username:password@localhost:5432/chatbot_db");
    expect(config.database.host).toBe("localhost");
    expect(config.database.port).toBe(5432);
    expect(config.database.name).toBe("chatbot_db");
    expect(config.database.user).toBe("username");
    expect(config.database.password).toBe("");
    expect(config.database.ssl).toBe(false);
    expect(config.database.maxConnections).toBe(20);
  });

  it("should load database configuration from environment variables", () => {
    process.env.DATABASE_URL = "******************************/test_db";
    process.env.DB_HOST = "db";
    process.env.DB_PORT = "5433";
    process.env.DB_NAME = "test_db";
    process.env.DB_USER = "test_user";
    process.env.DB_PASSWORD = "test_password";
    process.env.DB_SSL = "true";
    process.env.DB_MAX_CONNECTIONS = "10";

    const newConfig = require("../../src/config").default;

    expect(newConfig.database.url).toBe("******************************/test_db");
    expect(newConfig.database.host).toBe("db");
    expect(newConfig.database.port).toBe(5433);
    expect(newConfig.database.name).toBe("test_db");
    expect(newConfig.database.user).toBe("test_user");
    expect(newConfig.database.password).toBe("test_password");
    expect(newConfig.database.ssl).toBe(true);
    expect(newConfig.database.maxConnections).toBe(10);
  });

  it("should load default security configuration", () => {
    expect(config.security.jwtSecret).toBe("your-super-secret-jwt-key");
    expect(config.security.bcryptRounds).toBe(12);
  });

  it("should load security configuration from environment variables", () => {
    process.env.JWT_SECRET = "new-secret";
    process.env.BCRYPT_ROUNDS = "10";

    const newConfig = require("../../src/config").default;

    expect(newConfig.security.jwtSecret).toBe("new-secret");
    expect(newConfig.security.bcryptRounds).toBe(10);
  });

  it("should load default service URLs", () => {
    expect(config.services.botInteractionUrl).toBe("http://localhost:3001");
    expect(config.services.chatServiceUrl).toBe("http://localhost:3002");
  });

  it("should load service URLs from environment variables", () => {
    process.env.BOT_INTERACTION_SERVICE_URL = "http://interaction:3001";
    process.env.CHAT_SERVICE_URL = "http://chat:3002";

    const newConfig = require("../../src/config").default;

    expect(newConfig.services.botInteractionUrl).toBe("http://interaction:3001");
    expect(newConfig.services.chatServiceUrl).toBe("http://chat:3002");
  });

  it("should load default logging configuration", () => {
    expect(config.logging.level).toBe("info");
    expect(config.logging.file).toBeUndefined();
  });

  it("should load logging configuration from environment variables", () => {
    process.env.LOG_LEVEL = "debug";
    process.env.LOG_FILE = "/var/log/app.log";

    const newConfig = require("../../src/config").default;

    expect(newConfig.logging.level).toBe("debug");
    expect(newConfig.logging.file).toBe("/var/log/app.log");
  });

  it("should load default Kafka brokers", () => {
    expect(config.kafka.brokers).toEqual(["localhost:9092"]);
    expect(config.kafka.groupId).toBe("bot-builder-service");
  });

  it("should load Kafka brokers from environment variables", () => {
    process.env.KAFKA_BROKERS = "kafka1:9092,kafka2:9092";
    process.env.KAFKA_GROUP_ID = "test-group";

    const newConfig = require("../../src/config").default;

    expect(newConfig.kafka.brokers).toEqual(["kafka1:9092", "kafka2:9092"]);
    expect(newConfig.kafka.groupId).toBe("test-group");
  });

  it("should generate ADMIN_USER_ID if not provided in environment", () => {
    delete process.env.ADMIN_USER_ID;
    const { ADMIN_USER_ID: newAdminUserId } = require("../../src/config");
    expect(newAdminUserId).toBeDefined();
    expect(typeof newAdminUserId).toBe("string");
    expect(newAdminUserId.length).toBeGreaterThan(0);
  });

  it("should use ADMIN_USER_ID from environment if provided", () => {
    process.env.ADMIN_USER_ID = "test-admin-uuid";
    const { ADMIN_USER_ID: newAdminUserId } = require("../../src/config");
    expect(newAdminUserId).toBe("test-admin-uuid");
  });
});
